"use client";

import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import { useNotification } from "../Notification";
import { ArrowLeft, Send, ImageIcon, Loader2, Smile } from "lucide-react";
import { formatRelativeTime } from "@/lib/date-utils";
import EmojiPicker, { EmojiClickData } from "emoji-picker-react";

interface Message {
  _id: string;
  senderId:
    | {
        _id: string;
        username: string;
        profileImage?: string;
      }
    | string;
  receiverId: string;
  content: string;
  isImage: boolean;
  createdAt: string;
  read: boolean;
  readAt?: string;
}

interface User {
  _id: string;
  username: string;
  profileImage?: string;
}

interface MessageThreadProps {
  userId: string;
  onBack: () => void;
  onMessageSent: () => void;
  isModal?: boolean;
}

export default function MessageThread({
  userId,
  onBack,
  onMessageSent,
  isModal = false,
}: MessageThreadProps) {
  const { data: session } = useSession();
  const { showNotification } = useNotification();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [messageText, setMessageText] = useState("");
  const [user, setUser] = useState<User | null>(null);
  const [uploading, setUploading] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/messages/${userId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch messages");
        }
        const data = await response.json();
        setMessages(data);
      } catch (error) {
        console.error("Error fetching messages:", error);
        showNotification("Failed to load messages", "error");
      } finally {
        setLoading(false);
      }
    };

    const fetchUser = async () => {
      try {
        const response = await fetch(`/api/messages/user/${userId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch user");
        }
        const data = await response.json();
        setUser(data);
      } catch (error) {
        console.error("Error fetching user:", error);
        showNotification("Could not load user information", "error");
      }
    };

    if (session?.user?.id && userId) {
      fetchMessages();
      fetchUser();
    }
  }, [session, userId, showNotification]);

  useEffect(() => {
    // Scroll to bottom when messages change
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Set up polling for typing indicators
  useEffect(() => {
    if (!session?.user?.id || !userId) return;

    const checkTypingStatus = async () => {
      try {
        const response = await fetch(
          `/api/messages/typing?receiverId=${userId}`
        );
        if (response.ok) {
          const data = await response.json();
          setOtherUserTyping(data.isTyping);
        }
      } catch (error) {
        console.error("Error checking typing status:", error);
      }
    };

    // Check immediately
    checkTypingStatus();

    // Then poll every 3 seconds
    const intervalId = setInterval(checkTypingStatus, 3000);

    return () => {
      clearInterval(intervalId);
      // Clear typing indicator when component unmounts
      if (session?.user?.id && userId) {
        fetch("/api/messages/typing", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            receiverId: userId,
            isTyping: false,
          }),
        }).catch((error) => {
          console.error("Error clearing typing status:", error);
        });
      }
    };
  }, [session, userId]);

  const handleSendMessage = async () => {
    if (!messageText.trim()) return;

    try {
      setSending(true);
      const response = await fetch(`/api/messages/${userId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: messageText,
          isImage: false,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to send message");
      }

      const newMessage = await response.json();
      setMessages((prev) => [...prev, newMessage]);
      setMessageText("");
      onMessageSent();
    } catch (error) {
      console.error("Error sending message:", error);
      showNotification("Failed to send message", "error");
    } finally {
      setSending(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleEmojiClick = (emojiData: EmojiClickData) => {
    setMessageText((prev) => prev + emojiData.emoji);
    setShowEmojiPicker(false);
  };

  // Handle typing indicator
  const handleTyping = () => {
    if (!isTyping) {
      setIsTyping(true);

      // Send typing indicator
      fetch("/api/messages/typing", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          receiverId: userId,
          isTyping: true,
        }),
      }).catch((error) => {
        console.error("Error sending typing status:", error);
      });
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to clear typing indicator after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);

      // Send stopped typing indicator
      fetch("/api/messages/typing", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          receiverId: userId,
          isTyping: false,
        }),
      }).catch((error) => {
        console.error("Error sending typing status:", error);
      });
    }, 3000);
  };

  const handleImageUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith("image/")) {
      showNotification("Only image files are allowed", "error");
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      showNotification("Image size should be less than 5MB", "error");
      return;
    }

    try {
      setUploading(true);

      // Get a presigned URL for S3 upload
      const presignedResponse = await fetch("/api/upload/r2", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          type: "message-image",
        }),
      });

      if (!presignedResponse.ok) {
        throw new Error("Failed to get upload URL");
      }

      const { uploadUrl, fileUrl } = await presignedResponse.json();

      // Upload the file to S3
      const uploadResponse = await fetch(uploadUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error("Failed to upload image");
      }

      // Send message with image URL
      const response = await fetch(`/api/messages/${userId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: fileUrl,
          isImage: true,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to send image message");
      }

      const newMessage = await response.json();
      setMessages((prev) => [...prev, newMessage]);
      onMessageSent();
      showNotification("Image sent successfully", "success");
    } catch (error) {
      console.error("Error uploading image:", error);
      showNotification("Failed to send image", "error");
    } finally {
      setUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-3 border-b border-base-300 flex items-center">
        {!isModal && (
          <button
            type="button"
            onClick={onBack}
            className="btn btn-ghost btn-sm btn-circle mr-2"
            aria-label="Go back"
            title="Go back"
          >
            <ArrowLeft size={18} />
          </button>
        )}
        <div className="flex items-center">
          <div className="avatar mr-2">
            <div className="w-8 h-8 rounded-full">
              {user?.profileImage ? (
                <img
                  src={user.profileImage}
                  alt={user.username}
                  className="object-cover w-full h-full rounded-full chat-avatar-img"
                />
              ) : (
                <div className="bg-primary text-primary-content flex items-center justify-center w-full h-full rounded-full">
                  {user?.username?.charAt(0).toUpperCase() || "?"}
                </div>
              )}
            </div>
          </div>
          <span className="font-medium">{user?.username || "Loading..."}</span>
        </div>
      </div>

      <div className="flex-1 p-3">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <Loader2 className="w-6 h-6 animate-spin text-primary" />
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>No messages yet</p>
            <p className="text-sm">Send a message to start the conversation</p>
          </div>
        ) : (
          <div className="space-y-2">
            {messages.map((message) => {
              // Handle both populated and non-populated senderId
              const senderId =
                typeof message.senderId === "object"
                  ? message.senderId._id
                  : message.senderId;
              const isSender = senderId === session?.user?.id;
              return (
                <div
                  key={message._id}
                  className={`flex items-end gap-2 mb-3 ${
                    isSender ? "justify-end" : "justify-start"
                  }`}
                >
                  {!isSender && typeof message.senderId === "object" && (
                    <div className="avatar">
                      <div className="w-8 h-8 rounded-full">
                        {message.senderId.profileImage ? (
                          <img
                            src={message.senderId.profileImage}
                            alt={message.senderId.username}
                            className="object-cover w-full h-full rounded-full chat-avatar-img"
                          />
                        ) : (
                          <div className="bg-primary text-primary-content flex items-center justify-center w-full h-full rounded-full">
                            {message.senderId.username.charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  <div
                    className={`max-w-[80%] rounded-lg p-3 shadow-sm ${
                      isSender
                        ? "bg-primary text-primary-content"
                        : "bg-base-200"
                    }`}
                  >
                    {!isSender && typeof message.senderId === "object" && (
                      <div className="text-xs font-semibold mb-1">
                        {message.senderId.username}
                      </div>
                    )}
                    {message.isImage ? (
                      <img
                        src={message.content}
                        alt="Message image"
                        className="max-w-full rounded max-h-[200px]"
                      />
                    ) : (
                      <p className="whitespace-pre-wrap break-words">
                        {message.content}
                      </p>
                    )}
                    <div className="flex justify-between items-center">
                      <div
                        className={`text-xs mt-1 ${
                          isSender ? "text-primary-content/70" : "text-gray-500"
                        }`}
                      >
                        {formatRelativeTime(new Date(message.createdAt))}
                      </div>

                      {/* Read receipt for sender's messages */}
                      {isSender && (
                        <div className="text-xs mt-1 ml-2">
                          {message.read ? (
                            <span
                              title={
                                message.readAt
                                  ? `Read at ${new Date(
                                      message.readAt
                                    ).toLocaleString()}`
                                  : "Read"
                              }
                            >
                              ✓✓
                            </span>
                          ) : (
                            <span title="Sent">✓</span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            {otherUserTyping && (
              <div className="flex justify-start mb-3">
                <div className="bg-base-200 rounded-lg p-2 max-w-[80%] shadow-sm">
                  <div className="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      <div className="p-3 border-t border-base-300">
        {/* Emoji picker */}
        {showEmojiPicker && (
          <div className="absolute bottom-16 left-4 z-10" ref={emojiPickerRef}>
            <EmojiPicker
              onEmojiClick={handleEmojiClick}
              width={280}
              height={350}
              previewConfig={{ showPreview: false }}
            />
          </div>
        )}

        <div className="flex items-center flex-wrap sm:flex-nowrap">
          <button
            type="button"
            onClick={handleImageUpload}
            className="btn btn-ghost btn-sm btn-circle mr-1"
            disabled={uploading}
            title="Upload image"
            aria-label="Upload image"
          >
            {uploading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <ImageIcon size={18} aria-hidden="true" />
            )}
          </button>

          <button
            type="button"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            className="btn btn-ghost btn-sm btn-circle mr-2"
            title="Add emoji"
            aria-label="Add emoji"
          >
            <Smile size={18} aria-hidden="true" />
          </button>

          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleFileChange}
            aria-label="Upload image"
            title="Upload image"
          />
          <div className="flex-1 relative w-full mt-2 sm:mt-0">
            <input
              type="text"
              value={messageText}
              onChange={(e) => {
                setMessageText(e.target.value);
                handleTyping();
              }}
              onKeyDown={handleKeyDown}
              placeholder="Type a message..."
              className="input input-bordered input-sm w-full mr-2"
              disabled={sending || uploading}
            />
          </div>
          <button
            type="button"
            onClick={handleSendMessage}
            className="btn btn-primary btn-sm btn-circle"
            disabled={!messageText.trim() || sending || uploading}
            title="Send message"
            aria-label="Send message"
          >
            {sending ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send size={16} aria-hidden="true" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
