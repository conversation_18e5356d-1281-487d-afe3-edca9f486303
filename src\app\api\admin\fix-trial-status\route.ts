import { NextRequest, NextResponse } from "next/server";
import { dbconnect } from "@/lib/db";
import { Community } from "@/models/Community";

export async function POST(request: NextRequest) {
  try {
    await dbconnect();

    // Find communities with freeTrialActivated but no paymentStatus
    const result = await Community.updateMany(
      {
        freeTrialActivated: true,
        $or: [
          { paymentStatus: { $exists: false } },
          { paymentStatus: null },
          { paymentStatus: "" },
        ],
        subscriptionEndDate: { $gt: new Date() },
      },
      {
        $set: {
          paymentStatus: "trial",
        },
      }
    );

    console.log(
      `Updated ${result.modifiedCount} communities with trial status`
    );

    // Also check specific community
    const trialTestCommunity = await Community.findOne({ slug: "trial-test" });
    console.log("trial-test community after update:", {
      slug: trialTestCommunity?.slug,
      freeTrialActivated: trialTestCommunity?.freeTrialActivated,
      paymentStatus: trialTestCommunity?.paymentStatus,
      subscriptionEndDate: trialTestCommunity?.subscriptionEndDate,
    });

    return NextResponse.json({
      success: true,
      message: `Updated ${result.modifiedCount} communities with trial status`,
      trialTestCommunity: {
        slug: trialTestCommunity?.slug,
        freeTrialActivated: trialTestCommunity?.freeTrialActivated,
        paymentStatus: trialTestCommunity?.paymentStatus,
        subscriptionEndDate: trialTestCommunity?.subscriptionEndDate,
      },
    });
  } catch (error: any) {
    console.error("Error fixing trial status:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fix trial status" },
      { status: 500 }
    );
  }
}
